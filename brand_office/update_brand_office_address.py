#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cập nhật địa chỉ brand_office dựa trên geometry data
Task: Sử dụng dữ liệu geometry để xác định chính xác xã/phường và tỉnh/thành phố
"""

import json
import logging
import pandas as pd
import geopandas as gpd
import mysql.connector
from mysql.connector import Error
from shapely.geometry import Point, shape
import warnings
from gemini import Gemini
import asyncio
warnings.filterwarnings('ignore')


# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exports/update_address.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BrandOfficeAddressUpdater:
    def __init__(self):
        self.connection = None
        self.results = []
        self.batch_size = 400  # Reduced batch size as requested
        self.processed_count = 0
        self.matched_count = 0
        self.unmatched_count = 0
        self.gemini = Gemini()
        logger.info(f"🔧 Simple batch processing enabled (batch_size: {self.batch_size})")

    def get_database_connection(self):
        """Tạo kết nối database"""
        try:
            connection = mysql.connector.connect(
                host='127.0.0.1',
                port=3306,
                database='urbox',
                user='root',
                password='root',
                charset='utf8mb4'
            )
            
            if connection.is_connected():
                logger.info("✅ Kết nối database thành công!")
                return connection
                
        except Error as e:
            logger.error(f"❌ Lỗi kết nối database: {e}")
            return None
    
    def get_brand_office_data(self, offset=0, limit=400):
        """Lấy dữ liệu brand_office theo batch"""
        query = """
        SELECT id, latitude, longitude, city_id, address_old
        FROM brand_office
        WHERE address_old IS NOT NULL
        AND address_old != ''
        AND latitude IS NOT NULL
        AND longitude IS NOT NULL
        and status = 2
        LIMIT %s OFFSET %s
        """

        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, (limit, offset))
            results = cursor.fetchall()
            cursor.close()

            logger.info(f"📊 Lấy được {len(results)} records brand_office (offset: {offset})")
            return results

        except Error as e:
            logger.error(f"❌ Lỗi lấy dữ liệu brand_office: {e}")
            return []

    def get_total_brand_office_count(self):
        """Lấy tổng số records brand_office để tính toán số batch"""
        query = """
        SELECT COUNT(*) as total
        FROM brand_office
        WHERE address_old IS NOT NULL
        AND address_old != ''
        AND latitude IS NOT NULL
        AND longitude IS NOT NULL
        and status = 2
        """

        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query)
            result = cursor.fetchone()
            cursor.close()

            total = result['total'] if result else 0
            logger.info(f"📊 Tổng số records brand_office: {total}")
            return total

        except Error as e:
            logger.error(f"❌ Lỗi đếm brand_office: {e}")
            return 0

    def get_geo_ward_data(self):
        """Lấy dữ liệu geo_ward và trả về GeoDataFrame với spatial index"""

        query = """
        SELECT geometry, geo_province_code, province_title, ward_title, code
        FROM geo_ward
        WHERE geometry IS NOT NULL
        """

        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query)
            results = cursor.fetchall()
            cursor.close()

            logger.info(f"📊 Lấy được {len(results)} geo_ward records")

            # Chuyển đổi sang GeoDataFrame
            gdf_data = []
            for row in results:
                try:
                    # Parse geometry từ JSON string
                    if isinstance(row['geometry'], str):
                        geometry_data = json.loads(row['geometry'])
                    else:
                        geometry_data = row['geometry']

                    geometry = shape(geometry_data)

                    gdf_data.append({
                        'geometry': geometry,
                        'geo_province_code': row['geo_province_code'],
                        'province_title': row['province_title'],
                        'ward_title': row['ward_title'],
                        'code': row['code']
                    })
                except Exception as e:
                    logger.warning(f"⚠️ Lỗi parse geometry cho ward {row.get('code', 'unknown')}: {e}")
                    continue

            if not gdf_data:
                logger.error("❌ Không có geometry hợp lệ nào")
                return gpd.GeoDataFrame()

            # Tạo GeoDataFrame
            gdf = gpd.GeoDataFrame(gdf_data, crs='EPSG:4326')

            # Tạo spatial index để tối ưu hóa tìm kiếm
            logger.info("🔍 Tạo spatial index cho GeoDataFrame...")
            gdf.sindex  # Trigger spatial index creation
            logger.info(f"✅ Đã tạo GeoDataFrame với {len(gdf)} records và spatial index")
            return gdf

        except Error as e:
            logger.error(f"❌ Lỗi lấy geo_ward data: {e}")
            return gpd.GeoDataFrame()
    
    def parse_geometry(self, geometry_str):
        """Parse geometry từ JSON string thành Shapely object"""
        try:
            if isinstance(geometry_str, str):
                geometry_data = json.loads(geometry_str)
            else:
                geometry_data = geometry_str
                
            return shape(geometry_data)
        except Exception as e:
            logger.warning(f"⚠️ Lỗi parse geometry: {e}")
            return None
    
    def find_ward_by_lat_lng(self, lat, lng, geo_ward_gdf):
        """Tìm ward chứa tọa độ sử dụng spatial index (tối ưu hóa)"""

        try:
            point = Point(lng, lat)

            if len(geo_ward_gdf) == 0:
                return None, 'no_data'

            logger.debug(f"🔍 Tìm kiếm spatial index trong {len(geo_ward_gdf)} wards cho tọa độ ({lat}, {lng})")

            # Sử dụng spatial index để tìm candidates nhanh chóng
            try:
                # Lấy possible matches từ spatial index
                possible_matches_idx = list(geo_ward_gdf.sindex.intersection(point.bounds))

                if not possible_matches_idx:
                    logger.debug(f"🔍 Không tìm thấy candidates từ spatial index")
                    
                    return None, 'no_match'

                logger.debug(f"🔍 Spatial index tìm được {len(possible_matches_idx)} candidates")

                # Kiểm tra chính xác các candidates
                for idx in possible_matches_idx:
                    try:
                        ward_row = geo_ward_gdf.iloc[idx]
                        geometry = ward_row.geometry

                        if geometry:
                            # Thử contains() trước (chính xác hơn)
                            if geometry.contains(point):
                                ward_dict = ward_row.to_dict()
                                
                                return ward_dict, 'contains'
                            # Fallback: intersects() (cho trường hợp point ở biên)
                            elif geometry.intersects(point):
                                ward_dict = ward_row.to_dict()
                                
                                return ward_dict, 'intersects'
                    except Exception as candidate_error:
                        logger.warning(f"⚠️ Lỗi xử lý candidate {idx}: {candidate_error}")
                        continue

                # Fallback: buffer search cho các candidates
                for idx in possible_matches_idx:
                    try:
                        ward_row = geo_ward_gdf.iloc[idx]
                        geometry = ward_row.geometry

                        if geometry and geometry.buffer(0.001).intersects(point):
                            ward_dict = ward_row.to_dict()
                            
                            return ward_dict, 'buffer'
                    except Exception as buffer_error:
                        logger.warning(f"⚠️ Lỗi buffer search candidate {idx}: {buffer_error}")
                        continue

                
                return None, 'no_match'

            except Exception as spatial_error:
                logger.warning(f"⚠️ Lỗi spatial index, fallback to linear search: {spatial_error}")
                # Fallback to linear search nếu spatial index lỗi
            
                return None, 'error'

        except Exception as e:
            logger.warning(f"⚠️ Lỗi tìm ward tại ({lat}, {lng}): {e}")
            return None, 'error'

    async def process_batch_async(self, batch_data, geo_ward_data):
        """Xử lý một batch dữ liệu với async concurrent Gemini calls"""
        results = []

        # Bước 1: Tìm ward cho tất cả records (sync part)
        records_with_ward = []
        records_without_ward = []

        for record in batch_data:
            try:
                # Tìm ward theo tọa độ
                ward_info, _ = self.find_ward_by_lat_lng(
                    float(record['latitude']),
                    float(record['longitude']),
                    geo_ward_data
                )

                if ward_info and isinstance(ward_info, dict):
                    # Có tìm thấy ward
                    ward_title = ward_info.get('ward_title', '')
                    province_title = ward_info.get('province_title', '')
                    ward_code = ward_info.get('code', '')
                    geo_province_code = ward_info.get('geo_province_code', '')

                    records_with_ward.append({
                        'record': record,
                        'ward_title': ward_title,
                        'province_title': province_title,
                        'ward_code': ward_code,
                        'geo_province_code': geo_province_code
                    })
                else:
                    # Không tìm thấy ward
                    records_without_ward.append(record)

            except Exception as e:
                logger.error(f"❌ Lỗi tìm ward cho record {record.get('id', 'unknown')}: {e}")
                records_without_ward.append(record)

        # Bước 2: Xử lý records không có ward
        for record in records_without_ward:
            result = {
                'id': record['id'],
                'latitude': record['latitude'],
                'longitude': record['longitude'],
                'city_id': record['city_id'],
                'geo_province_code': '',
                'ward_code': '',
                'province_title': '',
                'ward_title': '',
                'address_old': record['address_old'],
                'new_address': 'NO_WARD_FOUND',
                'status': 'unmatched'
            }
            results.append(result)
            self.unmatched_count += 1

        # Bước 3: Xử lý records có ward với concurrent Gemini calls
        if records_with_ward:
            logger.info(f"🚀 Xử lý {len(records_with_ward)} records với Gemini concurrent...")

            # Tạo chat instance một lần
            chat = await self.gemini.start_chat()

            # Tạo tasks cho concurrent processing
            async def process_single_record(item):
                try:
                    record = item['record']
                    new_address = await self.gemini.convert_address(
                        chat, record, item['ward_title'], item['province_title']
                    )

                    return {
                        'id': record['id'],
                        'latitude': record['latitude'],
                        'longitude': record['longitude'],
                        'city_id': record['city_id'],
                        'geo_province_code': item['geo_province_code'],
                        'ward_code': item['ward_code'],
                        'province_title': item['province_title'],
                        'ward_title': item['ward_title'],
                        'address_old': record['address_old'],
                        'new_address': new_address,
                        'status': 'matched'
                    }
                except Exception as e:
                    logger.warning(f"⚠️ Lỗi Gemini cho record {item['record']['id']}: {e}")
                    record = item['record']
                    return {
                        'id': record['id'],
                        'latitude': record['latitude'],
                        'longitude': record['longitude'],
                        'city_id': record['city_id'],
                        'geo_province_code': item['geo_province_code'],
                        'ward_code': item['ward_code'],
                        'province_title': item['province_title'],
                        'ward_title': item['ward_title'],
                        'address_old': record['address_old'],
                        'new_address': 'GEMINI_ERROR',
                        'status': 'error'
                    }

            # Chạy concurrent tasks
            tasks = [process_single_record(item) for item in records_with_ward]
            gemini_results = await asyncio.gather(*tasks, return_exceptions=True)

            # Xử lý kết quả
            for result in gemini_results:
                if isinstance(result, Exception):
                    logger.error(f"❌ Exception trong concurrent processing: {result}")
                    continue

                results.append(result)
                if result['status'] == 'matched':
                    self.matched_count += 1
                else:
                    self.unmatched_count += 1

        self.processed_count += len(batch_data)

        # Log progress
        if self.processed_count % 200 == 0:  # Log mỗi 200 records
            logger.info(f"🔄 Đã xử lý {self.processed_count} records...")

        return results

    def save_results_to_csv(self, results, filename='brand_office_updated.csv'):
        """Lưu kết quả ra CSV"""
        try:
            df = pd.DataFrame(results)
            filepath = f"exports/{filename}"
            df.to_csv(filepath, index=False, encoding='utf-8')
            
            logger.info(f"✅ Đã lưu {len(results)} records vào {filepath}")
            
            # Thống kê
            matched = len([r for r in results if r['status'] == 'matched'])
            unmatched = len([r for r in results if r['status'] == 'unmatched'])
            
            logger.info(f"📊 THỐNG KÊ: Matched: {matched}, Unmatched: {unmatched}")
            
        except Exception as e:
            logger.error(f"❌ Lỗi lưu CSV: {e}")

    async def run(self):
        """Chạy toàn bộ process đơn giản với async Gemini"""
        try:
            logger.info("🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)")

            # Kết nối database
            self.connection = self.get_database_connection()
            if not self.connection:
                return

            # Lấy dữ liệu geo_ward
            logger.info("📊 Lấy dữ liệu geometry...")
            geo_ward_data = self.get_geo_ward_data()

            if geo_ward_data.empty:
                logger.error("❌ Không có dữ liệu geo_ward")
                return

            # Lấy tổng số records
            total_records = self.get_total_brand_office_count()
            total_batches = (total_records + self.batch_size - 1) // self.batch_size

            logger.info("=" * 60)
            logger.info("🔄 XỬ LÝ RECORDS")
            logger.info("=" * 60)
            logger.info(f"📊 Tổng records: {total_records}")
            logger.info(f"📊 Batch size: {self.batch_size}")
            logger.info(f"📊 Tổng batches: {total_batches}")

            all_results = []
            offset = 0
            batch_num = 1

            # Xử lý từng batch
            while True:
                logger.info(f"🔄 Xử lý batch {batch_num}/{total_batches} (offset: {offset})")

                # Lấy dữ liệu batch
                batch_data = self.get_brand_office_data(offset, self.batch_size)
                if not batch_data:
                    logger.info("✅ Đã xử lý hết dữ liệu")
                    break

                # Xử lý batch với async
                batch_results = await self.process_batch_async(batch_data, geo_ward_data)
                all_results.extend(batch_results)

                logger.info(f"✅ Hoàn thành batch {batch_num}: {len(batch_results)} records")

                # Lưu kết quả tạm thời mỗi 5 batches
                if batch_num % 5 == 0:
                    temp_filename = f'brand_office_temp_batch_{batch_num}.csv'
                    self.save_results_to_csv(all_results, temp_filename)
                    logger.info(f"� Đã lưu tạm {len(all_results)} records vào {temp_filename}")

                # Sleep 10 giây sau mỗi batch để tránh rate limit
                logger.info("😴 Nghỉ 10 giây để tránh rate limit...")
                await asyncio.sleep(10)

                offset += self.batch_size
                batch_num += 1

            # Lưu kết quả cuối cùng
            final_filename = 'brand_office_updated_final.csv'
            self.save_results_to_csv(all_results, final_filename)

            # Thống kê cuối cùng
            logger.info("🎉 HOÀN THÀNH CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE!")
            logger.info(f"📊 Tổng xử lý: {self.processed_count}")
            logger.info(f"📊 Matched: {self.matched_count}")
            logger.info(f"📊 Unmatched: {self.unmatched_count}")
            logger.info(f"📊 File kết quả: exports/{final_filename}")

            # Log final Gemini statistics
            self.gemini.log_final_stats()

        except Exception as e:
            logger.error(f"❌ Lỗi trong quá trình xử lý: {e}")
        finally:
            if self.connection:
                self.connection.close()

if __name__ == "__main__":
    updater = BrandOfficeAddressUpdater()
    asyncio.run(updater.run())
