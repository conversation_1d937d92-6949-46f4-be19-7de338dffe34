#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cập nhật địa chỉ brand_office dựa trên geometry data
Task: Sử dụng dữ liệu geometry để xác định chính xác xã/phường và tỉnh/thành phố
"""

import json
import logging
import pandas as pd
import geopandas as gpd
import mysql.connector
from mysql.connector import Error
from shapely.geometry import Point, shape
import warnings
from gemini import Gemini
warnings.filterwarnings('ignore')


# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exports/update_address.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BrandOfficeAddressUpdater:
    def __init__(self):
        self.connection = None
        self.results = []
        self.batch_size = 400  # Reduced batch size as requested
        self.processed_count = 0
        self.matched_count = 0
        self.unmatched_count = 0
        self.gemini = Gemini()
        logger.info(f"🔧 Simple batch processing enabled (batch_size: {self.batch_size})")

    def get_database_connection(self):
        """Tạo kết nối database"""
        try:
            connection = mysql.connector.connect(
                host='127.0.0.1',
                port=3306,
                database='urbox',
                user='root',
                password='root',
                charset='utf8mb4'
            )
            
            if connection.is_connected():
                logger.info("✅ Kết nối database thành công!")
                return connection
                
        except Error as e:
            logger.error(f"❌ Lỗi kết nối database: {e}")
            return None
    
    def get_brand_office_data(self, offset=0, limit=400):
        """Lấy dữ liệu brand_office theo batch"""
        query = """
        SELECT id, latitude, longitude, city_id, address_old
        FROM brand_office
        WHERE address_old IS NOT NULL
        AND address_old != ''
        AND latitude IS NOT NULL
        AND longitude IS NOT NULL
        and status = 2
        LIMIT %s OFFSET %s
        """

        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, (limit, offset))
            results = cursor.fetchall()
            cursor.close()

            logger.info(f"📊 Lấy được {len(results)} records brand_office (offset: {offset})")
            return results

        except Error as e:
            logger.error(f"❌ Lỗi lấy dữ liệu brand_office: {e}")
            return []

    def get_total_brand_office_count(self):
        """Lấy tổng số records brand_office để tính toán số batch"""
        query = """
        SELECT COUNT(*) as total
        FROM brand_office
        WHERE address_old IS NOT NULL
        AND address_old != ''
        AND latitude IS NOT NULL
        AND longitude IS NOT NULL
        and status = 2
        """

        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query)
            result = cursor.fetchone()
            cursor.close()

            total = result['total'] if result else 0
            logger.info(f"📊 Tổng số records brand_office: {total}")
            return total

        except Error as e:
            logger.error(f"❌ Lỗi đếm brand_office: {e}")
            return 0

    def get_geo_ward_data(self):
        """Lấy dữ liệu geo_ward và trả về GeoDataFrame với spatial index"""

        query = """
        SELECT geometry, geo_province_code, province_title, ward_title, code
        FROM geo_ward
        WHERE geometry IS NOT NULL
        """

        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query)
            results = cursor.fetchall()
            cursor.close()

            logger.info(f"📊 Lấy được {len(results)} geo_ward records")

            # Chuyển đổi sang GeoDataFrame
            gdf_data = []
            for row in results:
                try:
                    # Parse geometry từ JSON string
                    if isinstance(row['geometry'], str):
                        geometry_data = json.loads(row['geometry'])
                    else:
                        geometry_data = row['geometry']

                    geometry = shape(geometry_data)

                    gdf_data.append({
                        'geometry': geometry,
                        'geo_province_code': row['geo_province_code'],
                        'province_title': row['province_title'],
                        'ward_title': row['ward_title'],
                        'code': row['code']
                    })
                except Exception as e:
                    logger.warning(f"⚠️ Lỗi parse geometry cho ward {row.get('code', 'unknown')}: {e}")
                    continue

            if not gdf_data:
                logger.error("❌ Không có geometry hợp lệ nào")
                return gpd.GeoDataFrame()

            # Tạo GeoDataFrame
            gdf = gpd.GeoDataFrame(gdf_data, crs='EPSG:4326')

            # Tạo spatial index để tối ưu hóa tìm kiếm
            logger.info("🔍 Tạo spatial index cho GeoDataFrame...")
            gdf.sindex  # Trigger spatial index creation
            logger.info(f"✅ Đã tạo GeoDataFrame với {len(gdf)} records và spatial index")
            return gdf

        except Error as e:
            logger.error(f"❌ Lỗi lấy geo_ward data: {e}")
            return gpd.GeoDataFrame()
    
    def parse_geometry(self, geometry_str):
        """Parse geometry từ JSON string thành Shapely object"""
        try:
            if isinstance(geometry_str, str):
                geometry_data = json.loads(geometry_str)
            else:
                geometry_data = geometry_str
                
            return shape(geometry_data)
        except Exception as e:
            logger.warning(f"⚠️ Lỗi parse geometry: {e}")
            return None
    
    def find_ward_by_lat_lng(self, lat, lng, geo_ward_gdf):
        """Tìm ward chứa tọa độ sử dụng spatial index (tối ưu hóa)"""

        try:
            point = Point(lng, lat)

            if len(geo_ward_gdf) == 0:
                return None, 'no_data'

            logger.debug(f"🔍 Tìm kiếm spatial index trong {len(geo_ward_gdf)} wards cho tọa độ ({lat}, {lng})")

            # Sử dụng spatial index để tìm candidates nhanh chóng
            try:
                # Lấy possible matches từ spatial index
                possible_matches_idx = list(geo_ward_gdf.sindex.intersection(point.bounds))

                if not possible_matches_idx:
                    logger.debug(f"🔍 Không tìm thấy candidates từ spatial index")
                    
                    return None, 'no_match'

                logger.debug(f"🔍 Spatial index tìm được {len(possible_matches_idx)} candidates")

                # Kiểm tra chính xác các candidates
                for idx in possible_matches_idx:
                    try:
                        ward_row = geo_ward_gdf.iloc[idx]
                        geometry = ward_row.geometry

                        if geometry:
                            # Thử contains() trước (chính xác hơn)
                            if geometry.contains(point):
                                ward_dict = ward_row.to_dict()
                                
                                return ward_dict, 'contains'
                            # Fallback: intersects() (cho trường hợp point ở biên)
                            elif geometry.intersects(point):
                                ward_dict = ward_row.to_dict()
                                
                                return ward_dict, 'intersects'
                    except Exception as candidate_error:
                        logger.warning(f"⚠️ Lỗi xử lý candidate {idx}: {candidate_error}")
                        continue

                # Fallback: buffer search cho các candidates
                for idx in possible_matches_idx:
                    try:
                        ward_row = geo_ward_gdf.iloc[idx]
                        geometry = ward_row.geometry

                        if geometry and geometry.buffer(0.001).intersects(point):
                            ward_dict = ward_row.to_dict()
                            
                            return ward_dict, 'buffer'
                    except Exception as buffer_error:
                        logger.warning(f"⚠️ Lỗi buffer search candidate {idx}: {buffer_error}")
                        continue

                
                return None, 'no_match'

            except Exception as spatial_error:
                logger.warning(f"⚠️ Lỗi spatial index, fallback to linear search: {spatial_error}")
                # Fallback to linear search nếu spatial index lỗi
            
                return None, 'error'

        except Exception as e:
            logger.warning(f"⚠️ Lỗi tìm ward tại ({lat}, {lng}): {e}")
            return None, 'error'

    def process_batch_simple(self, batch_data, geo_ward_data):
        """Xử lý một batch dữ liệu đơn giản (sync)"""
        results = []

        for record in batch_data:
            try:
                # Tìm ward theo tọa độ
                ward_info, _ = self.find_ward_by_lat_lng(
                    float(record['latitude']),
                    float(record['longitude']),
                    geo_ward_data
                )

                if ward_info and isinstance(ward_info, dict):
                    # Có tìm thấy ward
                    ward_title = ward_info.get('ward_title', '')
                    province_title = ward_info.get('province_title', '')
                    ward_code = ward_info.get('code', '')
                    geo_province_code = ward_info.get('geo_province_code', '')

                    # Gọi Gemini để convert địa chỉ (sync)
                    try:
                        new_address = self.gemini.convert_address(
                            record, ward_title, province_title
                        )
                    except Exception as gemini_error:
                        logger.warning(f"⚠️ Lỗi Gemini cho record {record['id']}: {gemini_error}")
                        new_address = "GEMINI_ERROR"

                    result = {
                        'id': record['id'],
                        'latitude': record['latitude'],
                        'longitude': record['longitude'],
                        'city_id': record['city_id'],
                        'geo_province_code': geo_province_code,
                        'ward_code': ward_code,
                        'province_title': province_title,
                        'ward_title': ward_title,
                        'address_old': record['address_old'],
                        'new_address': new_address,
                        'status': 'matched'
                    }
                    self.matched_count += 1
                else:
                    # Không tìm thấy ward
                    result = {
                        'id': record['id'],
                        'latitude': record['latitude'],
                        'longitude': record['longitude'],
                        'city_id': record['city_id'],
                        'geo_province_code': '',
                        'ward_code': '',
                        'province_title': '',
                        'ward_title': '',
                        'address_old': record['address_old'],
                        'new_address': 'NO_WARD_FOUND',
                        'status': 'unmatched'
                    }
                    self.unmatched_count += 1

                results.append(result)
                self.processed_count += 1

                # Log progress mỗi 50 records
                if self.processed_count % 50 == 0:
                    logger.info(f"🔄 Đã xử lý {self.processed_count} records...")

            except Exception as e:
                logger.error(f"❌ Lỗi xử lý record {record.get('id', 'unknown')}: {e}")
                # Thêm record lỗi
                result = {
                    'id': record.get('id', 'unknown'),
                    'latitude': record.get('latitude', ''),
                    'longitude': record.get('longitude', ''),
                    'city_id': record.get('city_id', ''),
                    'geo_province_code': '',
                    'ward_code': '',
                    'province_title': '',
                    'ward_title': '',
                    'address_old': record.get('address_old', ''),
                    'new_address': 'PROCESSING_ERROR',
                    'status': 'error'
                }
                results.append(result)
                continue

        return results

    def save_results_to_csv(self, results, filename='brand_office_updated.csv'):
        """Lưu kết quả ra CSV"""
        try:
            df = pd.DataFrame(results)
            filepath = f"exports/{filename}"
            df.to_csv(filepath, index=False, encoding='utf-8')
            
            logger.info(f"✅ Đã lưu {len(results)} records vào {filepath}")
            
            # Thống kê
            matched = len([r for r in results if r['status'] == 'matched'])
            unmatched = len([r for r in results if r['status'] == 'unmatched'])
            
            logger.info(f"📊 THỐNG KÊ: Matched: {matched}, Unmatched: {unmatched}")
            
        except Exception as e:
            logger.error(f"❌ Lỗi lưu CSV: {e}")

    def run_simple(self):
        """Chạy toàn bộ process đơn giản (sync)"""
        try:
            logger.info("🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)")

            # Kết nối database
            self.connection = self.get_database_connection()
            if not self.connection:
                return

            # Lấy dữ liệu geo_ward
            logger.info("📊 Lấy dữ liệu geometry...")
            geo_ward_data = self.get_geo_ward_data()

            if geo_ward_data.empty:
                logger.error("❌ Không có dữ liệu geo_ward")
                return

            # Lấy tổng số records
            total_records = self.get_total_brand_office_count()
            total_batches = (total_records + self.batch_size - 1) // self.batch_size

            logger.info("=" * 60)
            logger.info("🔄 XỬ LÝ RECORDS")
            logger.info("=" * 60)
            logger.info(f"📊 Tổng records: {total_records}")
            logger.info(f"📊 Batch size: {self.batch_size}")
            logger.info(f"📊 Tổng batches: {total_batches}")

            all_results = []
            offset = 0
            batch_num = 1

            # Xử lý từng batch
            while True:
                logger.info(f"🔄 Xử lý batch {batch_num}/{total_batches} (offset: {offset})")

                # Lấy dữ liệu batch
                batch_data = self.get_brand_office_data(offset, self.batch_size)
                if not batch_data:
                    logger.info("✅ Đã xử lý hết dữ liệu")
                    break

                # Xử lý batch
                batch_results = self.process_batch_simple(batch_data, geo_ward_data)
                all_results.extend(batch_results)

                logger.info(f"✅ Hoàn thành batch {batch_num}: {len(batch_results)} records")

                # Lưu kết quả tạm thời mỗi 5 batches
                if batch_num % 5 == 0:
                    temp_filename = f'brand_office_temp_batch_{batch_num}.csv'
                    self.save_results_to_csv(all_results, temp_filename)
                    logger.info(f"� Đã lưu tạm {len(all_results)} records vào {temp_filename}")

                offset += self.batch_size
                batch_num += 1

            # Lưu kết quả cuối cùng
            final_filename = 'brand_office_updated_final.csv'
            self.save_results_to_csv(all_results, final_filename)

            # Thống kê cuối cùng
            logger.info("🎉 HOÀN THÀNH CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE!")
            logger.info(f"📊 Tổng xử lý: {self.processed_count}")
            logger.info(f"📊 Matched: {self.matched_count}")
            logger.info(f"📊 Unmatched: {self.unmatched_count}")
            logger.info(f"📊 File kết quả: exports/{final_filename}")

        except Exception as e:
            logger.error(f"❌ Lỗi trong quá trình xử lý: {e}")
        finally:
            if self.connection:
                self.connection.close()
    

    
    async def process_large_dataset(self, data_stream: AsyncGenerator, geo_ward_data, output_file='exports/large_dataset_results.csv'):

        logger.info("🚀 Starting large dataset processing với parallel chunk processing...")

        # Initialize CSV file with headers
        headers = ['id', 'latitude', 'longitude', 'city_id',
                'geo_province_code',  'ward_code', 'province_title', 'ward_title', 'address_old', 'new_address', 'status']
        pd.DataFrame(columns=headers).to_csv(output_file, index=False)

        total_processed = 0
        total_success = 0
        total_errors = 0

        # Sequential processing to maintain exactly 2000 RPM target

        async def processed_stream_generator():
            """Generator to perform geometry matching on the input stream"""
            async for batch in data_stream:
                for record in batch:
                    ward_info = None
                    try:
                        ward_info, _ = self.find_ward_by_lat_lng(
                            float(record['latitude']),
                            float(record['longitude']),
                            geo_ward_data
                        )
                     
                        if ward_info and isinstance(ward_info, dict):
                            # Validate required fields
                            required_fields = ['ward_title', 'province_title', 'code', 'geo_province_code']
                            if all(field in ward_info for field in required_fields):
                                yield {
                                    'brand_office': record,
                                    'ward': ward_info['ward_title'],
                                    'province': ward_info['province_title'],
                                    'ward_code': ward_info['code'],
                                    'geo_province_code': ward_info['geo_province_code']
                                }
                            else:
                                logger.warning(f"⚠️ Ward info missing required fields: {ward_info}")
                        else:
                            # No ward found or invalid ward_info
                            pass
                    except Exception as e:
                        logger.error(f"❌ Error in processed_stream_generator for record {record.get('id', 'unknown')}: {e}")
                        continue

        # Sequential chunk processing to maintain exactly 2000 RPM
        async for chunk_results in self.process_stream(processed_stream_generator(), chunk_size=50):
            # Convert to proper format
            formatted_results = []
            for result in chunk_results:
                try:
                    # Validate result structure
                    if not isinstance(result, dict):
                        logger.error(f"❌ Result is not dict: {type(result)} - {result}")
                        continue

                    formatted_result = {
                        'id': result.get('id', '-2'),
                        'latitude': result.get('latitude', ''),
                        'longitude': result.get('longitude', ''),
                        'city_id': result.get('city_id', ''),
                        'geo_province_code': result.get('geo_province_code', ''),
                        'ward_code': result.get('ward_code', ''),
                        'province_title': result.get('province', ''),
                        'ward_title': result.get('ward', ''),
                        'address_old': result.get('original_address', ''),
                        'new_address': result.get('new_address', ''),
                        'status': result.get('status', 'matched')
                    }
                    formatted_results.append(formatted_result)
                except Exception as format_error:
                    logger.error(f"❌ Error formatting result: {format_error}")
                    logger.error(f"   Result: {result}")
                    continue

            # Append to CSV immediately (streaming write)
            if formatted_results:
                chunk_df = pd.DataFrame(formatted_results)
                chunk_df.to_csv(output_file, mode='a', header=False, index=False)

            # Update stats - safe access
            chunk_success = len([
                r for r in chunk_results
                if isinstance(r, dict) and r.get('status') == 'success'
            ])
            chunk_errors = len(chunk_results) - chunk_success

            total_processed += len(chunk_results)
            total_success += chunk_success
            total_errors += chunk_errors

            # Log chunk stats
            logger.info(f"✅ Chunk completed: {len(chunk_results)} records | "
                       f"Success: {chunk_success} | Errors: {chunk_errors}")

        # Final stats
        success_rate = total_success / total_processed * 100 if total_processed > 0 else 0

        logger.info(f"🎉 Large dataset processing completed!")
        logger.info(f"📊 Final stats:")
        logger.info(f"   - Total processed: {total_processed}")
        logger.info(f"   - Success: {total_success} ({success_rate:.1f}%)")
        logger.info(f"   - Errors: {total_errors}")
        logger.info(f"   - Output file: {output_file}")

        return {
            'total_processed': total_processed,
            'total_success': total_success,
            'total_errors': total_errors,
            'success_rate': success_rate,
            'output_file': output_file
        }
        
    async def run(self):
        """Chạy toàn bộ process với async support"""
        try:
            logger.info("🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE")

            # Kết nối database
            self.connection = self.get_database_connection()
            if not self.connection:
                return

            # Lấy dữ liệu mapping và geo_ward
            logger.info("📊 Lấy dữ liệu mapping và geometry...")
            geo_ward_data = self.get_geo_ward_data()

            if geo_ward_data.empty:
                logger.error("❌ Không có dữ liệu geo_ward")
                return

            logger.info("" + "="*60)
            logger.info("🔄 XỬ LÝ RECORDS")
            logger.info("="*60)
            logger.info("🧠 Using memory optimization for processing")
            data_stream = self.stream_brand_office_data()

            memory_results = await self.process_large_dataset(
                data_stream, geo_ward_data,
                output_file='exports/brand_offices_updated.csv'
            )
    
            logger.info(f"✅ UPDATED BRAND OFFICE ADDRESS COMPLETED! Processed: {memory_results.get('total_processed', 0)}")

            # Log final Gemini statistics
            self.gemini.log_final_stats()

        except Exception as e:
            logger.error(f"❌ Lỗi trong quá trình xử lý: {e}")
        finally:
            if self.connection:
                self.connection.close()

if __name__ == "__main__":
    import asyncio
    updater = BrandOfficeAddressUpdater()
    asyncio.run(updater.run())
