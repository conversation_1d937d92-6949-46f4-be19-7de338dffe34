import random
import google.generativeai as genai
import logging
import asyncio
import time
import requests
from dotenv import load_dotenv
import os
load_dotenv()

genai.configure(api_key=os.getenv('GEMINI_API_KEY'))    

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exports/brand_office_gemini.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Gemini:
    def __init__(self, model='gemini-2.5-flash-lite', max_concurrent=10):
        self.model = genai.GenerativeModel(model)
        self.chat = self.model.start_chat(history=[])
        self.request_count = 0
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)

        # Performance monitoring
        self.start_time = time.time()
        self.success_count = 0
        self.error_count = 0

    def build_conversion_prompt(self):
        """<PERSON><PERSON><PERSON> dựng một prompt chi tiết cho Gemini để đảm bảo kết quả chính xác.
    <PERSON><PERSON><PERSON> là "bộ não" của tác vụ.
        """
        # Sử dụng kỹ thuật "few-shot prompting" bằng cách đưa ra vài ví dụ
        # để AI hiểu rõ định dạng và yêu cầu.
        prompt = f"""
Bạn là một chuyên gia xử lý và chuẩn hóa địa chỉ tại Việt Nam.
Nhiệm vụ của bạn là nhận một địa chỉ cũ và thông tin xã/tỉnh mới, sau đó tạo ra một địa chỉ mới theo các quy tắc nghiêm ngặt sau:

1.  **Giữ nguyên** các chi tiết của địa chỉ cũ như: số nhà, tên đường, ngõ, ngách, hẻm, thôn, xóm, ấp.
2.  **Xóa bỏ hoàn toàn** thông tin về Quận/Huyện/Thị xã cũ.
3.  **Thay thế** hoặc **bổ sung** thông tin Xã/Phường bằng "Xã/Phường mới" được cung cấp.
4.  **Thay thế** hoặc **bổ sung** thông tin Tỉnh/Thành phố bằng "Tỉnh/Thành phố mới" được cung cấp.
5.  **Chỉ trả về chuỗi địa chỉ mới**, không thêm bất kỳ lời giải thích hay câu chữ thừa nào.
6.  **Tên** Xã' hoặc 'Phường' hoặc 'Thị Trấn' ỏ địa chỉ cũ **có thể được viết tắt** là 'X.' hoặc 'P. hoặc TT. Hãy **chuyển đổi** chúng về dạng đầy đủ.
---
**Ví dụ 1:**
- Địa chỉ cũ: Thôn Đoài, xã Minh Trí, huyện Sóc Sơn, Thành phố Hà Nội
- Xã/Phường mới: Xã Tân Minh
- Tỉnh/Thành phố mới: Thành phố Hà Nội
- KẾT QUẢ ĐÚNG: Thôn Đoài, Xã Tân Minh, Thành phố Hà Nội

**Ví dụ 2:**
- Địa chỉ cũ: Số 25, phố Tràng Tiền, quận Hoàn Kiếm, Hà Nội
- Xã/Phường mới: Phường Tràng Tiền
- Tỉnh/Thành phố mới: Thành phố Hà Nội
- KẾT QUẢ ĐÚNG: Số 25, phố Tràng Tiền, Phường Tràng Tiền, Thành phố Hà Nội

**Ví dụ 3:**
- Địa chỉ cũ: Thôn 5, xã Ea M'nang, huyện Cư M'gar, tỉnh Đắk Lắk
- Xã/Phường mới: Xã Dliêya
- Tỉnh/Thành phố mới: Tỉnh Đắk Nông
- KẾT QUẢ ĐÚNG: Thôn 5, Xã Dliêya, Tỉnh Đắk Nông
---

**Ví dụ 4:**
- Địa chỉ cũ: A1/6A2 Quốc lộ 50, X.Bình Hưng, H.Bình Chánh, TP.Hồ Chí Minh
- Xã/Phường mới: Xã Bình Hưng
- Tỉnh/Thành phố mới: Thành phố Hồ Chí Minh
- KẾT QUẢ ĐÚNG: A1/6A2 Quốc lộ 50, Xã Bình Hưng, Thành phố Hồ Chí Minh
---

**Sau tin nhắn này, tôi sẽ bắt đầu gửi dữ liệu. Hãy sẵn sàng.**
"""
        return prompt
    async def start_chat(self):
        chat = self.model.start_chat(history=[])
        # 1. GỬI PROMPT HỆ THỐNG (DÀI) CHỈ MỘT LẦN
        logger.info("Đang gửi hướng dẫn ban đầu cho Gemini...")
        initial_prompt = self.build_conversion_prompt()
        # Chúng ta không cần phản hồi của tin nhắn này, chỉ cần "dạy" cho AI
        await chat.send_message_async(initial_prompt)
        return chat
    
    async def convert_address(self, chat, brand_office, ward, province):
        """
        Optimized async function với separate chat instances để tránh race condition.
        """
        request_start_time = time.time()

        async with self.semaphore:  # Limit concurrent requests
            try:
                # Tạo chat instance riêng cho request này để tránh race condition

                # Mock data for local testing
                if os.getenv('MOCK_GEMINI', 'false').lower() == 'true':
                    # Mock response for testing
                    class MockResponse:
                        def __init__(self, text):
                            request  = requests.get(f'http://localhost:3003/api/address?address={brand_office['address_old']}')
                            response = request.json()
                            if response['status'] == 'success':
                                self.text = response['address']
                            else:
                                self.text = 'CONVERSION_ERROR'
                                logger.error(f"❌ OVER RATE LIMIT {brand_office['id']}: {response['message']}")
                    # Prepare and send main message
                    short_message = self.format_subsequent_message(
                        brand_office['address_old'],
                        ward,
                        province
                    )

                    response = MockResponse(brand_office['address_old'])
                    await asyncio.sleep(random.uniform(0.3, 1))  # Simulate API delay
                else:
                    short_message = self.format_subsequent_message(
                        brand_office['address_old'],
                        ward,
                        province
                    )
                    response = await asyncio.wait_for(
                        chat.send_message_async(short_message),
                        timeout=10.0  # 10 second timeout for faster failure detection
                    )
                    await asyncio.sleep(0.05) 

                # Update counters
                self.request_count += 1
                self.success_count += 1

                # Calculate response time
                response_time = time.time() - request_start_time

                # Optimized logging - only every 100 requests
                if self.request_count % 100 == 0:
                    elapsed = time.time() - self.start_time
                    actual_rpm = self.request_count / (elapsed / 60) if elapsed > 0 else 0
                    success_rate = self.success_count / self.request_count * 100
                    logger.info(f"📊 Progress: {self.request_count} requests | {actual_rpm:.1f} RPM | {success_rate:.1f}% success | {response_time:.2f}s response")

                return response.text.strip()

            except asyncio.TimeoutError:
                self.error_count += 1
                response_time = time.time() - request_start_time
                logger.warning(f"⏰ Timeout for record {brand_office['id']} after {response_time:.2f}s")
                return "TIMEOUT_ERROR"
            except Exception as e:
                self.error_count += 1
                response_time = time.time() - request_start_time
                logger.error(f"❌ Error processing record {brand_office['id']} after {response_time:.2f}s: {e}")
                return "CONVERSION_ERROR"
    
    def format_subsequent_message(self, old_address, new_commune, new_province):
        """
        Định dạng tin nhắn ngắn gọn cho mỗi địa chỉ sau tin nhắn đầu tiên.
        """
        return f"""
    - Địa chỉ cũ: "{old_address}"
    - Xã/Phường mới: "{new_commune}"
    - Tỉnh/Thành phố mới: "{new_province}"
    """
    
  
# Khởi tạo instance gemini
# Chỉ khởi tạo khi được import, không phải khi chạy trực tiếp
if __name__ != "__main__":
    try:
        gemini = Gemini()
        logger.info("✅ Gemini instance đã được khởi tạo thành công")
    except Exception as e:
        logger.error(f"❌ Lỗi khởi tạo Gemini instance: {e}")
        gemini = None
