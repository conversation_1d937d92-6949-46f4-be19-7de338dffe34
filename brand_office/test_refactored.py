#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script để kiểm tra refactored version
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from update_brand_office_address import BrandOfficeAddressUpdater
import logging

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_refactored_version():
    """Test refactored version với 1 batch nhỏ"""
    try:
        logger.info("🧪 TESTING REFACTORED VERSION")

        updater = BrandOfficeAddressUpdater()

        # Test kết nối database
        connection = updater.get_database_connection()
        if not connection:
            logger.error("❌ Không thể kết nối database")
            return

        updater.connection = connection

        # Test lấy tổng số records
        total_count = updater.get_total_brand_office_count()
        logger.info(f"📊 Tổng số records: {total_count}")

        # Test lấy 1 batch nhỏ
        test_batch = updater.get_brand_office_data(offset=0, limit=5)
        logger.info(f"📊 Test batch: {len(test_batch)} records")

        # Test lấy geo_ward data
        geo_ward_data = updater.get_geo_ward_data()
        logger.info(f"📊 Geo ward data: {len(geo_ward_data)} records")

        if not geo_ward_data.empty and test_batch:
            # Test duplicate detection
            logger.info("🔄 Testing duplicate detection...")
            test_filename = 'test_duplicates.csv'
            processed_ids = updater.get_processed_ids(test_filename)
            logger.info(f"📊 Processed IDs: {len(processed_ids)}")

            # Test get data with exclude_ids
            logger.info("🔄 Testing get_brand_office_data with exclude_ids...")
            test_batch_filtered = updater.get_brand_office_data(offset=0, limit=5, exclude_ids=processed_ids)
            logger.info(f"📊 Filtered batch: {len(test_batch_filtered)} records")

            # Test xử lý 1 batch nhỏ với async
            logger.info("🔄 Testing async batch processing...")
            results = await updater.process_batch_async(test_batch_filtered, geo_ward_data)
            logger.info(f"✅ Processed {len(results)} records")

            # Test sleep (chỉ 2 giây cho test)
            logger.info("😴 Test sleep 2 giây...")
            import asyncio
            await asyncio.sleep(2)

            # Hiển thị kết quả mẫu
            for i, result in enumerate(results[:2]):  # Chỉ hiển thị 2 kết quả đầu
                logger.info(f"📋 Result {i+1}:")
                logger.info(f"   ID: {result['id']}")
                logger.info(f"   Old: {result['address_old'][:50]}...")
                logger.info(f"   New: {result['new_address'][:50]}...")
                logger.info(f"   Status: {result['status']}")

        logger.info("✅ TEST COMPLETED SUCCESSFULLY!")

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
    finally:
        if updater.connection:
            updater.connection.close()

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_refactored_version())
